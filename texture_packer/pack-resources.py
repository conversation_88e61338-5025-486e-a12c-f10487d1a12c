#!/usr/bin/python

import os
import shutil
import subprocess

if __name__ == '__main__':
    current_dir = os.path.dirname(os.path.realpath(__file__))
    project_dir = os.path.join(current_dir, '..')

    # Pack.
    packer_path = os.path.join(
        project_dir,
        'submodules',
        'ee-scripts',
        'pack.js'
    )

    subprocess.Popen([
        'node',
        packer_path,
        '-t',
        'remote',
        '-a',
        'http://senspark.com:3456',
        '-i',
        os.path.join(project_dir, 'texture_packer', 'images', 'texture_packer.json'),
        '-o',
        os.path.join(project_dir, 'texture_packer', 'packed')
    ]).wait()

    # Copy resources (PNG) for editor.
    resource_dir = os.path.join(
        project_dir,
        'assets', 'resources', 'packed'
    )
    png_pack_dir = os.path.join(
        project_dir,
        'texture_packer',
        'packed', 'ipadhd'
    )

    for d in os.listdir(png_pack_dir):
        if not d.startswith('.'):
            src = os.path.join(png_pack_dir, d)
            dst = os.path.join(resource_dir, d)
            if os.path.exists(dst):
               os.remove(dst)
            shutil.copy(src, dst)