#!/usr/bin/python

import os
import shutil
import subprocess

if __name__ == '__main__':
    current_dir = os.path.dirname(os.path.realpath(__file__))
    project_dir = os.path.join(current_dir, '..')

    # Pack.
    packer_path = os.path.join(
        project_dir,
        'submodules',
        'ee-scripts',
        'pack.js'
    )

    subprocess.Popen([
        'node',
        packer_path,
        '-t',
        'remote',
        '-a',
        'http://senspark.com:3456',
        '-i',
        os.path.join(project_dir, 'texture_packer', 'ads', 'texture_packer.json'),
        '-o',
        os.path.join(project_dir, 'assets', 'resources', 'ads'),
    ]).wait()
