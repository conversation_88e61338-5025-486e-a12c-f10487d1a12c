#!/usr/bin/python

import os
import shutil
import subprocess

if __name__ == '__main__':
    current_dir = os.path.dirname(os.path.realpath(__file__))
    project_dir = os.path.join(current_dir, '..')

    # Pack.
    script_path = os.path.join(
        project_dir,
        'submodules',
        'ee-scripts',
        'replace_resources.js'
    )

    subprocess.Popen([
        'node',
        script_path,
        '-m',
        os.path.join(project_dir, 'assets', 'resources', 'packed'),
        '-i',
        os.path.join(project_dir, 'texture_packer', 'packed', 'pvr', 'ipadhd'),
        '-o',
        os.path.join(project_dir, 'build', 'jsb-default', 'res', 'raw-assets')
    ]).wait()