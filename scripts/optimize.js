const path = require('path');
const glob = require('glob')
const jsonfile = require('jsonfile');
const currentDir = __dirname;
const projectDir = path.join(currentDir, '..');
const buildDir = path.join(projectDir, 'build', 'jsb-default');
const resDir = path.join(buildDir, 'res');
glob(`${resDir}/**/*.json`, (err, files) => {
    Promise.all(files.map(async file => {
        const content = await jsonfile.readFile(file);
        await jsonfile.writeFile(file, content, {
            spaces: 0,
        });
    }));
})