const glob = require('glob');
const fs = require('fs-extra');
const path = require('path');
const util = require('util');

const currentDir = __dirname;
const projectDir = path.join(currentDir, '..');
const resourcesDir = path.join(projectDir, 'assets', 'resources');
const itemDir = path.join(resourcesDir, 'prefabs', 'level_items');
const levelDir = path.join(resourcesDir, 'prefabs', 'levels');

const BOOSTER_CLOCK             /**/ = 'clock';
const BOOSTER_DYNAMITE          /**/ = 'dynamite';

const BOOSTER_DIAMOND_POLISH    /**/ = 'diamond_polish';
const BOOSTER_LUCKY_CLOVER      /**/ = 'lucky_clover';
const BOOSTER_OIL_CAN           /**/ = 'oil_can';
const BOOSTER_STRENGTH_DRINK    /**/ = 'strength_drink';
const BOOSTER_TITAN_ROPE        /**/ = 'titan_rope';

const BOOSTER_PIRATE_HAT        /**/ = 'pirate_hat';
const BOOSTER_SPIRIT_JAR        /**/ = 'spirit_jar';
const BOOSTER_SPOOKY_DOLL       /**/ = 'spooky_doll';

const BOOSTER_ANT_SPRAY         /**/ = 'ant_spray';
const BOOSTER_WANTED_POSTER     /**/ = 'wanted_poster';

const BOOSTER_GLOVE             /**/ = 'glove';
const BOOSTER_OLD_HAM           /**/ = 'old_ham';

/** @type {string[]} */
const boosterTypes = [
    // Not appear in select level dialog.
    BOOSTER_CLOCK,

    // Only appear if out of dynamite.
    BOOSTER_DYNAMITE,

    // Common boosters.
    BOOSTER_DIAMOND_POLISH,
    BOOSTER_LUCKY_CLOVER,
    BOOSTER_OIL_CAN,
    BOOSTER_STRENGTH_DRINK,
    BOOSTER_TITAN_ROPE,

    BOOSTER_PIRATE_HAT,
    BOOSTER_SPIRIT_JAR,
    BOOSTER_SPOOKY_DOLL,

    BOOSTER_ANT_SPRAY,
    BOOSTER_WANTED_POSTER,

    BOOSTER_GLOVE,
    BOOSTER_OLD_HAM,
];

/** @type {Array.<[RegExp, string[]]>} */
const itemBoosterRegexes = [
    [/^coral_diamond$/        /**/, [BOOSTER_DIAMOND_POLISH]],
    [/^diamond(Big)?$/        /**/, [BOOSTER_DIAMOND_POLISH]],
    [/^diamond_mouse$/        /**/, [BOOSTER_DIAMOND_POLISH]],
    [/^niece_(0|thanks|trapped|)$/, [BOOSTER_DIAMOND_POLISH]],
    [/^bag_[01]$/             /**/, [BOOSTER_LUCKY_CLOVER]],
    [/^railway_\d$/           /**/, [BOOSTER_OIL_CAN]],
    [/./                      /**/, [BOOSTER_STRENGTH_DRINK]],
    [/^gold_4$/               /**/, [BOOSTER_TITAN_ROPE]],
    [/^stone_2$/              /**/, [BOOSTER_TITAN_ROPE]],
    [/^wall_\d$/              /**/, [BOOSTER_TITAN_ROPE]],

    [/^mussel$/               /**/, [BOOSTER_PIRATE_HAT]],
    [/^ghost_0$/              /**/, [BOOSTER_SPIRIT_JAR]],
    [/^ghost_1$/              /**/, [BOOSTER_SPOOKY_DOLL]],

    [/^ant$/                  /**/, [BOOSTER_ANT_SPRAY]],
    [/^thief$/                /**/, [BOOSTER_WANTED_POSTER]],

    [/^hedgehog$/             /**/, [BOOSTER_GLOVE]],
    [/^vulture_red$/          /**/, [BOOSTER_OLD_HAM]],
];

/** @param {string} itemName */
const parseItemBoosters = itemName => {
    /** @type {string[]} */
    const itemBoosters = [];
    let matches = 0;
    for (let i = 0, n = itemBoosterRegexes.length; i < n; ++i) {
        const [regex, boosters] = itemBoosterRegexes[i];
        if (regex.test(itemName)) {
            itemBoosters.push(...boosters);
            ++matches;
        }
    }
    if (itemBoosters.length === 0) {
        // console.log(`Cannot find booster for ${itemName}`);
        return null;
    }
    if (matches >= 2) {
        // console.log(`${itemName} matches multiple regexes.`);
    }
    return itemBoosters;
};

const process = async () => {
    /** @type {Object.<number, Object.<number, string[]>>} */
    const levelBoosters = {};

    /** @type {Object.<string, { uuid: string, boosters: string[]}>} */
    const items = {};

    /** @type {Object.<string, string>} */
    const uuidToItemName = {};

    // Parse items.
    await util.promisify(glob)(`${itemDir}/*.meta`).then(async filePaths => {
        await Promise.all(filePaths.map(async filePath => {
            const fileName = path.parse(filePath).base;
            const regex = /([\w_]+)\.prefab/
            const matches = regex.exec(fileName);
            const itemName = matches[1];

            /** @type {{uuid: string}} */
            const content = await fs.readJson(filePath);
            const uuid = content.uuid;
            const boosters = parseItemBoosters(itemName);
            items[itemName] = {
                uuid,
                boosters,
            };
            uuidToItemName[uuid] = itemName;
        }));
    });

    const itemNames = Object.keys(items);
    const uuids = Object.keys(uuidToItemName);

    // Parse story levels.
    for (let area = 0; area < 12; ++area) {
        const pattern = `${levelDir}/area_${area + 1}/*.prefab`;
        await util.promisify(glob)(pattern).then(async filePaths => {
            await Promise.all(filePaths.map(async filePath => {
                const fileName = path.parse(filePath).base;
                const regex = /level_([\d]+)\.prefab/
                const matches = regex.exec(fileName);
                const level = parseInt(matches[1]) - 1;

                /** @type {Object.<string, number>>} */
                const itemBoosters = {};

                const content = await fs.readFile(filePath, 'utf8');
                uuids.forEach(uuid => {
                    const index = content.indexOf(uuid);
                    if (index === -1) {
                        return;
                    }
                    // Found.
                    const itemName = uuidToItemName[uuid];
                    const item = items[itemName];
                    if (item.boosters === null) {
                        console.log(`Found ${itemName} in level ${area}/${level} but null type.`)
                        return;
                    }
                    item.boosters.forEach(type => {
                        itemBoosters[type] = itemBoosters[type] || 0;
                        ++itemBoosters[type];
                    });

                    const areaInfo = levelBoosters[area] = levelBoosters[area] || {};
                    areaInfo[level] = Object.keys(itemBoosters);
                });

                // console.log(`${area}/${level} = ${JSON.stringify(itemBoosters)}`);
            }));
        });
    }

    return levelBoosters;
};

process().then(boosters => {
    fs.writeJsonSync(path.join(currentDir, 'level_boosters.json'), boosters);
});