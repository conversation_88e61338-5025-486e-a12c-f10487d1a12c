const assert = require('assert');
const faker = require('faker');
const mysql = require('mysql');
// faker API locale.
const LOCALES = ['az', 'cz', 'de', 'de_AT', 'de_CH', 'en', 'en_AU', 'en_BORK', 'en_CA', 'en_GB', 'en_IE', 'en_IND', 'en_US', 'en_au_ocker', 'es', 'es_MX', 'fa', 'fr', 'fr_CA', 'ge', 'id_ID', 'it', 'ja', 'ko', 'nb_NO', 'nep', 'nl', 'pl', 'pt_BR', 'ru', 'sk', 'sv', 'tr', 'uk', 'vi', 'zh_CN', 'zh_TW'];
// Level targets.
const LEVEL_TARGETS = [
    [
        900,
        900,
        900,
        1000,
        1000,
        900,
        900,
        1200,
        1400,
        1000,
        1400,
        1600,
        1300,
        1500,
        1700,
        2400,
        2400,
        2400
    ],
    [
        1100,
        1400,
        1600,
        1900,
        1200,
        1500,
        1700,
        2100,
        1300,
        1700,
        2200,
        1500,
        1900,
        2400,
        1700,
        2600,
        2000,
        2800
    ],
    [
        1320,
        846,
        2072,
        1820,
        2213,
        2930,
        700,
        800,
        1610,
        1920,
        1501,
        3126,
        1650,
        1080,
        1700,
        2585,
        2527,
        4093
    ],
    [
        1450,
        1200,
        2220,
        1900,
        1414,
        1300,
        1720,
        820,
        1917,
        1690,
        2975,
        2360,
        2250,
        900,
        2508,
        1450,
        1700,
        1300
    ],
    [
        1700,
        1193,
        1320,
        1762,
        2430,
        3062,
        1801,
        1650,
        2040,
        2098,
        2302,
        3000,
        1920,
        1450,
        2360,
        3965,
        2190,
        5105
    ],
    [
        500,
        810,
        1590,
        2295,
        1332,
        3089,
        1620,
        800,
        1860,
        2632,
        2458,
        1430,
        837,
        1012,
        1935,
        1490,
        2225,
        2570
    ],
    [
        1203,
        1642,
        1053,
        1498,
        2103,
        2326,
        1023,
        2192,
        2216,
        1395,
        1994,
        2380,
        700,
        1145,
        1820,
        2016,
        2085,
        2717
    ],
    [
        1530,
        1725,
        1860,
        2035,
        1834,
        2738,
        1820,
        800,
        2114,
        1676,
        2642,
        3620,
        1530,
        1672,
        2630,
        3262,
        3626,
        4450
    ],
    [
        1120,
        1635,
        1862,
        2176,
        1766,
        2921,
        1450,
        2372,
        1373,
        1433,
        1972,
        2730,
        2450,
        2620,
        2660,
        2585,
        2527,
        3200
    ],
    [
        1000,
        1100,
        600,
        1000,
        800,
        600,
        1000,
        800,
        800,
        600,
        1000,
        1000,
        800,
        1500,
        1200,
        2100,
        2000,
        2400
    ],
    [
        1200,
        1400,
        1500,
        900,
        1100,
        2400,
        2000,
        1600,
        2000,
        1400,
        1000,
        2200,
        1200,
        3000,
        2000,
        1800,
        2100,
        3600
    ],
    [
        2000,
        2200,
        2400,
        2600,
        2000,
        2400,
        2600,
        2800,
        2800,
        2400,
        2400,
        2600,
        2800,
        1800,
        2400,
        3000,
        3000,
        3800
    ]
];
// Asynchronously sleeps for the specified amount of duration.
function sleep(milliseconds) {
    return new Promise(resolve => setTimeout(resolve, milliseconds));
}
// Generates a random number in the specified interval.
const random = (from, to) => {
    return Math.random() * (to - from) + from;
};
// Returns [score, star].
const randomizeScore = target => {
    const targets = [
        target,
        Math.floor(target * 1.2),
        Math.floor(target * 1.4),
    ];
    const score = Math.floor(random(target, target * 1.5));
    for (let i = targets.length - 1; i >= 0; --i) {
        if (score >= targets[i]) {
            return [score, i + 1];
        }
    }
    assert(false);
};
const conn = mysql.createConnection({
    // host: 'localhost',
    host: '*************',
    // host: 'gold-miner-vegas.senspark.com',
    port: 3306,
    user: 'gmv',
    password: '12354',
    database: 'gmv',
    insecureAuth: true,
    multipleStatements: true,
});
// Number of areas.
const TOTAL_AREAS = 12;
// Number of levels per area.
const LEVELS_PER_AREA = 18;
// Number of levels.
const TOTAL_LEVELS = TOTAL_AREAS * LEVELS_PER_AREA;
// Create random level data.
const createData = () => {
    const data = [];
    const unlockedWorldLevels = TOTAL_LEVELS; // Math.floor(random(1, TOTAL_LEVELS + 1));
    assert(1 <= unlockedWorldLevels && unlockedWorldLevels <= TOTAL_LEVELS);
    let totalScore = 0;
    for (let i = 0; i < TOTAL_AREAS; ++i) {
        const fromLevels = i * LEVELS_PER_AREA + 1;
        let unlockedLevels = Math.max(0, Math.min(LEVELS_PER_AREA, unlockedWorldLevels - fromLevels + 1));
        if (unlockedWorldLevels === fromLevels) {
            // Clear the last area.
            ++unlockedLevels;
        }
        if (unlockedLevels === 0) {
            const levels = [];
            for (let j = 0; j < LEVELS_PER_AREA; ++j) {
                levels[j] = { score: 0, star: 0 };
            }
            data[i] = { levels, unlockedLevels: 0 };
        } else {
            assert(1 <= unlockedLevels && unlockedLevels <= LEVELS_PER_AREA);
            const levels = [];
            for (let j = 0; j < LEVELS_PER_AREA; ++j) {
                if (j < unlockedLevels) {
                    const target = LEVEL_TARGETS[i][j];
                    assert(target);
                    const [score, star] = randomizeScore(target);
                    totalScore += score;
                    levels[j] = { score, star };
                } else {
                    levels[j] = { score: 0, star: 0 };
                }
            }
            data[i] = { levels, unlockedLevels };
        }
    }
    const info = {
        all_level_info: data,
        total_level_score: totalScore,
    };
    return info;
};
conn.connect(err => {
    console.log('connected');
    const statements = [];
    for (let i = 0; i < 50; ++i) {
        const userId = `fake_${Math.floor(random(1000000, 9999999))}`;
        const data = createData();
        const locale = LOCALES[Math.floor(random(0, LOCALES.length))];
        faker.locale = locale;
        const username = faker.name.findName().replace('\'', '\\\'');
        const timestamp = Math.floor(Date.now() / 1000);
        const statement = `
            INSERT INTO users (user_id, username, data, timestamp)
            VALUES ('${userId}', '${username}', '${JSON.stringify(data)}', ${timestamp})
        `;
        statements.push(statement);
    }
    conn.query(statements.join(';'), (err, results, fields) => {
        if (err) {
            console.log(err);
        } else if (results) {
            console.log(results);
        }
    });
});