'use strict';

const fs = require('fs');
const Electron = require("electron");
const {exec} = require('child_process');

// https://docs.cocos.com/creator/manual/en/publish/custom-project-build-template.html

let findItem = (array, pred) => {
    for (let item of array) {
        if (pred(item)) {
            return item;
        }
    }
    return undefined;
};

let findPrefabMenu = () => {
    let menu = Electron.Menu.getApplicationMenu();
    let eeMenu = findItem(menu.items, item => item.id === 'ee');
    let prefabMenu = findItem(eeMenu.submenu.items, item => item.position === 'prefab');
    return prefabMenu;
};

const INSTALL_EEX_PATH = '/jsb-default/frameworks/runtime-src/libraries/ts';

const onBeforeBuild = (options, callback) => {
    Editor.log(`Begin to build ee-x ts`);
    const eexPath = `${options.buildPath}${INSTALL_EEX_PATH}`;
    exec(`cd ${eexPath} && bash install.sh`, (err, stdout, stderr) => {
        if (err) {
            Editor.log(`Error: ${err}`);
            callback(err);
            return;
        }
        Editor.log(stdout);
        Editor.log(`ee-x ts built completed`);
        callback();
    });
};

// https://github.com/electron/electron/blob/master/docs/api/menu-item.md
module.exports = {
    load() {
        Editor.log(`ee-x plugin loaded`);
        this.listener = new Editor.IpcListener();
        this.listener.on('ee:profile_manager_loaded', event => {
            Editor.Ipc.sendToAll('ee:profile_manager:get_data', 'use_nested_prefab', 'ee:update_prefab_menu');
        });
        this.listener.on('ee:update_prefab_menu', (event, data) => {
            let menu = findPrefabMenu();
            menu.checked = data;
        });
        Editor.Builder.on('build-start', onBeforeBuild);
    },

    unload() {
        this.listener.clear();
        Editor.Builder.removeListener('build-start', onBeforeBuild);
    },

    messages: {
        use_nested_prefab: event => {
            let menu = findPrefabMenu();
            Editor.Ipc.sendToAll('ee:profile_manager:set_data', 'use_nested_prefab', menu.checked);
        },
    },
};