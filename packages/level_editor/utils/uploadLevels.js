const fs = require("fs");
const crypto = require("crypto");
const appRoot = require("app-root-path");

const BASE_PATH = `${appRoot}/assets/resources/levels_json`;
const EVENT_PATH = `${BASE_PATH}/event`;
const MAIN_PATH = `${BASE_PATH}/main`;
const JAKI_PATH = `${BASE_PATH}/jaki`;
const CHECKSUM_PATH = `${BASE_PATH}/levels_checksum.json`;
const UPLOAD_LOCAL_PATH = `http://localhost:9000/gm7/admin/upload-levels`;
const UPLOAD_PROD_PATH = `https://gm7-api.senspark.com/gm7/admin/upload-levels`;
const UPLOAD_TEST_PATH = `https://gm7-test.senspark.com/gm7/admin/upload-levels`;
const JWT =
  "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhdXRoMCIsImlhdCI6MTcyOTU4NTk4NSwicGxhdGZvcm0iOiJlZGl0b3IiLCJ1c2VybmFtZSI6ImVkaXRvci0xIn0.X5XVIs2qv-6k7SInD9wp-OhRDhDLWlWW-wfY8bdxVhI";

module.exports = {
  uploadLevels,
  UPLOAD_LOCAL_PATH,
  UPLOAD_PROD_PATH,
  UPLOAD_TEST_PATH,
};

async function uploadLevels(path) {
  try {
    Editor.log("Generating checksums...");
    const files = generateChecksumDirectory(
      [MAIN_PATH, EVENT_PATH, JAKI_PATH],
      CHECKSUM_PATH
    );
    Editor.log("Upload files...");
    files.push(CHECKSUM_PATH);
    return await uploadFiles(files, path);
  } catch (e) {
    Editor.error(`[uploadLevels]: ${e.message}`);
    return false;
  }
}

/**
 * @param filesPath {string[]}
 */
async function uploadFiles(filesPath, path) {
  const formData = new FormData();
  filesPath.forEach((fp) => {
    const fileName = fp.substring(fp.lastIndexOf("/") + 1);
    // console.log(`Uploading ${fileName}`);
    const f = new Blob([fs.readFileSync(fp)]);
    formData.append("file", f, fileName);
  });
  const reqOptions = {
    method: "POST",
    body: formData,
    headers: {
      Authorization: `Bearer ${JWT}`,
    },
  };

  const res = await fetch(path, reqOptions);
  return res.ok;
}

/**
 * @param directoryPath {string[]}
 * @param outputFilePath {string}
 * @return All json files path in directories {string[]}
 */
function generateChecksumDirectory(directoryPath, outputFilePath) {
  const checksums = {};
  const allFilesPaths = [];

  directoryPath.forEach((d) => {
    const files = getFileNamesInDirectory(d);
    betterStringSort(files);
    files.forEach((file) => {
      const fName = file.split(".");
      const fileNameWithoutExt = fName[0];
      const ext = fName[fName.length - 1];
      if (ext !== "json") {
        return;
      }
      const path = `${d}/${file}`;
      const checksum = generateChecksumFile(path);
      checksums[fileNameWithoutExt] = checksum;
      // console.log(`${fileNameWithoutExt}: ${checksum}`);
      allFilesPaths.push(path);
    });
  });

  const data = JSON.stringify(checksums);
  writeToFile(outputFilePath, data);
  return allFilesPaths;
}

/**
 * @param filePath {string}
 * @return {string}
 */
function generateChecksumFile(filePath) {
  const data = fs.readFileSync(filePath, "utf8");
  const hash = crypto.createHash("sha256");
  hash.update(data);
  return hash.digest("hex");
}

/**
 * @param path {string}
 * @return {string[]}
 */
function getFileNamesInDirectory(path) {
  return fs.readdirSync(path);
}

/**
 * @param filePath {string}
 * @param data {string}
 */
function writeToFile(filePath, data) {
  const directory = filePath.substring(0, filePath.lastIndexOf("/"));
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory);
  }
  fs.writeFileSync(filePath, data);
}

/**
 * @param strings {string[]}
 */
function betterStringSort(strings) {
  strings.sort((a, b) => {
    const regex = /(\d+)/g;

    // Split strings by numbers
    const aParts = a
      .split(regex)
      .map((part) => (isNaN(Number(part)) ? part : parseInt(part, 10)));
    const bParts = b
      .split(regex)
      .map((part) => (isNaN(Number(part)) ? part : parseInt(part, 10)));

    // Compare each part
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      if (aParts[i] !== bParts[i]) {
        return aParts[i] < bParts[i] ? -1 : 1;
      }
    }

    return 0;
  });
}
