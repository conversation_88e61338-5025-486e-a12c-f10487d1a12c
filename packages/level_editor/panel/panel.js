const fs = require("fs");
const configs = require("../configs");
const uploader = require("../utils/uploadLevels");
const {
  UPLOAD_TEST_PATH,
  UPLOAD_LOCAL_PATH,
  UPLOAD_PROD_PATH,
} = require("../utils/uploadLevels");

const PANEL_FOLDER = __dirname;
const MIN_AREA_LEVEL = 1;
const MAX_AREA_EVENT = 7;
const MAX_AREA_MAIN = 12;
const MAX_LEVEL = 20;

Editor.Panel.extend({
  template: fs.readFileSync(`${PANEL_FOLDER}/index.html`, "utf8"),

  $: {
    iptMode: "#iptMode",
    iptArea: "#iptArea",
    btnAreaPrev: "#btnAreaPrev",
    btnAreaNext: "#btnAreaNext",

    iptLevel: "#iptLevel",
    btnLevelPrev: "#btnLevelPrev",
    btnLevelNext: "#btnLevelNext",
    btnSubmitGroup1: "#btnSubmitGroup1",

    iptDropLevel: "#iptDropLevel",
    btnSubmitGroup2: "#btnSubmitGroup2",

    iptJsonFile: "#iptJsonFile",
    btnSubmitGroup3: "#btnSubmitGroup3",

    iptItemPrefab: "#iptItemPrefab",
    btnAddItem: "#btnAddItem",

    btnUploadLevelsToProd: "#btnUploadLevelsToProd",
    btnUploadLevelsToTest: "#btnUploadLevelsToTest",
    btnUploadLevelsToLocal: "#btnUploadLevelsToLocal",
  },

  ready() {
    this.$btnSubmitGroup1.addEventListener("confirm", () => {
      try {
        const mode = getInt(this.$iptMode.value, "Game Mode");
        const area = getInt(this.$iptArea.value, "Area");
        const level = getInt(this.$iptLevel.value, "Level");
        const data = JSON.stringify({ mode, area, level });
        Editor.Scene.callSceneScript(
          configs.Const.PackageName,
          configs.SceneMethods.OpenLevel,
          data
        );
      } catch (e) {
        Editor.error(`[btnSubmitGroup1]: ${e.message}`);
      }
    });

    this.$btnSubmitGroup3.addEventListener("confirm", () => {
      try {
        const uuid = this.$iptJsonFile.value;
        Editor.assetdb.queryPathByUuid(uuid, (err, url) => {
          if (err || !fs.existsSync(url)) {
            Editor.error(`File ko hợp lệ`);
            return;
          }
          const jsonData = fs.readFileSync(url, "utf8");
          const data = JSON.stringify({ jsonData });
          Editor.Scene.callSceneScript(
            configs.Const.PackageName,
            configs.SceneMethods.OpenLevel,
            data
          );
        });
      } catch (e) {
        Editor.error(`[btnSubmitGroup3]: ${e.message}`);
      }
    });

    this.$btnAreaPrev.addEventListener("confirm", () => {
      this.changeArea(-1);
    });
    this.$btnAreaNext.addEventListener("confirm", () => {
      this.changeArea(1);
    });
    this.$btnLevelPrev.addEventListener("confirm", () => {
      this.changeLevel(-1);
    });
    this.$btnLevelNext.addEventListener("confirm", () => {
      this.changeLevel(1);
    });

    this.$btnAddItem.addEventListener("confirm", () => {
      try {
        const prefabUuid = this.$iptItemPrefab.value;
        if (!prefabUuid) {
          Editor.error("Please select an item prefab first");
          return;
        }

        const data = JSON.stringify({
          prefabUuid,
        });

        Editor.Scene.callSceneScript(
          configs.Const.PackageName,
          configs.SceneMethods.AddItemToLevel,
          data
        );

        Editor.log(`Adding item`);
      } catch (e) {
        Editor.error(`[btnAddItem]: ${e.message}`);
      }
    });

    this.$btnUploadLevelsToProd.addEventListener("confirm", () => {
      uploader.uploadLevels(UPLOAD_PROD_PATH).then((r) => {
        r
          ? Editor.log(`Upload levels to ${UPLOAD_PROD_PATH} completed`)
          : Editor.error(`Upload levels to ${UPLOAD_PROD_PATH} failed`);
      });
    });
    this.$btnUploadLevelsToTest.addEventListener("confirm", () => {
      uploader.uploadLevels(UPLOAD_TEST_PATH).then((r) => {
        r
          ? Editor.log(`Upload levels to ${UPLOAD_TEST_PATH} completed`)
          : Editor.error(`Upload levels to ${UPLOAD_TEST_PATH} failed`);
      });
    });
    this.$btnUploadLevelsToLocal.addEventListener("confirm", () => {
      uploader.uploadLevels(UPLOAD_LOCAL_PATH).then((r) => {
        r
          ? Editor.log(`Upload levels to ${UPLOAD_LOCAL_PATH} completed`)
          : Editor.error(`Upload levels to ${UPLOAD_LOCAL_PATH} failed`);
      });
    });

    // this.$btn.addEventListener('confirm', () => {
    //     // this.$label.innerText = 'Hello World';
    //     // setTimeout(() => {
    //     //     this.$label.innerText = '--';
    //     // }, 500);
    //     // Editor.Ipc.sendToMain('level-editor:say-hello2', '1234546');
    //     // Editor.Scene.callSceneScript('level-editor', 'get-canvas-children', (err, length) => {
    //     //     this.$label.innerText = `Canvas has ${length} children`;
    //     // });
    //     this.saveLevel();
    // });
  },

  messages: {
    "scene:saved"() {
      Editor.Scene.callSceneScript(
        configs.Const.PackageName,
        configs.SceneMethods.TryExportLevelToJson
      );
    },
    "panel-level-scene-saved"(ev, args) {
      try {
        if (!args) {
          return;
        }
        const data = JSON.parse(args);
        Editor.log(
          `Level saved (${data.mode}): Area ${data.area} Level ${data.level}`
        );
      } catch (e) {
        Editor.error(`[level-scene-saved]: ${e.message}`);
      }
    },
  },

  changeArea(addition) {
    const data = this.getLevelData();
    let area = data.area + addition;
    let maxArea = 1;
    switch (data.mode) {
      case 0:
        maxArea = MAX_AREA_MAIN;
        break;
      case 1:
        maxArea = MAX_AREA_EVENT;
        break;
      case 2:
      default:
        maxArea = 1;
        break;
    }
    area = clamp(MIN_AREA_LEVEL, maxArea, area);
    this.$iptArea.value = area;
  },

  changeLevel(addition) {
    const data = this.getLevelData();
    let level = data.level + addition;
    level = clamp(MIN_AREA_LEVEL, MAX_LEVEL, level);
    this.$iptLevel.value = level;
  },

  getLevelData() {
    const mode = forceInt(this.$iptMode.value, 0);
    const area = forceInt(this.$iptArea.value, MIN_AREA_LEVEL);
    const level = forceInt(this.$iptLevel.value, MIN_AREA_LEVEL);
    return { mode, area, level };
  },
});

function getInt(str, ctrlName) {
  const val = parseInt(str);
  if (isNaN(val)) {
    throw new Error(`${ctrlName} Không phải là số`);
  }
  return val;
}

function forceInt(str, defaultValue) {
  const val = parseInt(str);
  return isNaN(val) ? defaultValue : val;
}

function clamp(minVal, maxVal, value) {
  return Math.min(maxVal, Math.max(minVal, value));
}
