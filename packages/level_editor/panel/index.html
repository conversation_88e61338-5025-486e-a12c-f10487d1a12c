<style>
    :host {
        margin: 5px;
    }

    h2 {
        color: #f90;
    }

    .group {
        margin: 5px;
    }

    .group > * {
        margin: 5px;
        display: block;
    }

    #divGroup1 {
        background: #426130;
    }

    #divGroup2 {
        background: #674d29;
    }

    #divGroup3 {
        background: #535347;
    }

    ui-drop-area {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        outline: none;

        width: 50px;
        height: 50px;
        padding: 15px;
        margin: 15px;

        border: 5px dotted #666;

        color: #888;
        font-size: 16px;
        font-weight: bold;
    }

    ui-drop-area[drag-hovering] {
        border-color: #090;
        color: #090;
    }

    ui-asset {
        width: 200px;
    }

    #divGroup4 {
        background: #2d4a66;
    }
</style>

<h2>Chọn 1 trong 2 cách sau:</h2>
<div class="layout vertical">
    <div id="divGroup1" class="group">
        <h3>Cách 1: Nhập số:</h3>
        <ui-select value="0" id="iptMode">
            <option value="0">Story (Main)</option>
            <option value="1">Event</option>
            <option value="2">Jaki</option>
        </ui-select>
        <div class="layout horizontal">
            <span class="label" style="width: 50px">Area</span>
            <ui-button id="btnAreaPrev">-</ui-button>
            <ui-num-input id="iptArea" value="1"></ui-num-input>
            <ui-button id="btnAreaNext">+</ui-button>
        </div>
        <div class="layout horizontal">
            <span class="label" style="width: 50px">Level</span>
            <ui-button id="btnLevelPrev">-</ui-button>
            <ui-num-input id="iptLevel" value="1"></ui-num-input>
            <ui-button id="btnLevelNext">+</ui-button>
        </div>
        <ui-button id="btnSubmitGroup1">Open Level</ui-button>
    </div>
    <!--    <div id="divGroup2" class="group">-->
    <!--        <h3>Kéo thả file ngoài project:</h3>-->
    <!--        <ui-drop-area id="iptDropLevel" droppable="file">file</ui-drop-area>-->
    <!--        <ui-button id="btnSubmitGroup2">Open Level</ui-button>-->
    <!--    </div>-->
    <div id="divGroup3" class="group">
        <h3>Cách 2: Kéo thả file trong Assets:</h3>
        <br/>
        <ui-asset id="iptJsonFile" type="json" empty></ui-asset>
        <ui-button id="btnSubmitGroup3">Open Level</ui-button>
    </div>

    <div id="divGroup4" class="group">
        <h3>Công cụ thêm item:</h3>
        <br/>
        <div class="layout horizontal">
            <span class="label" style="width: 70px">Item Prefab:</span>
            <ui-asset id="iptItemPrefab" type="prefab" empty></ui-asset>
        </div>
        <ui-button id="btnAddItem">Add Item to Level</ui-button>
    </div>
</div>
<hr/>
<div>Nhấn Ctrl+S (Cmd+S) để lưu level & export JSON</div>
<hr/>
<div
        class="layout horizontal"
        style="justify-content: center; margin-top: 10px"
>
    <ui-button id="btnUploadLevelsToProd">Upload to Prod</ui-button>
    <ui-button id="btnUploadLevelsToTest">Upload to Test</ui-button>
    <ui-button id="btnUploadLevelsToLocal" style="visibility: hidden"
    >Upload to Local
    </ui-button
    >
    <!--    <a href="">Chỉnh sửa Level Override</a>-->
</div>
