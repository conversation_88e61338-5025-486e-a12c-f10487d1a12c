const configs = require("../configs");

module.exports = {
    "scene-open-level": (event, args) => {
        try {
            const n = cc.find(configs.Const.ObjectManagerPath);
            if (!n) {
                openLevelEditorScene(args);
                return;
            }
            openLevel(n, args);
        } catch (e) {
            Editor.error(`[${configs.SceneMethods.OpenLevel}]: ${e.message}`);
        }
    },
    "scene-try-export-level-to-json": (event, args) => {
        try {
            if (!isLevelEditorScene()) {
                return;
            }
            const n = cc.find(configs.Const.ObjectManagerPath);
            if (!n) {
                return;
            }
            exportJson(n, args);
        } catch (e) {
            Editor.error(
                `[${configs.SceneMethods.TryExportLevelToJson}]: ${e.message}`
            );
        }
    },
    "scene-upload-levels": (event, args) => {
        try {
            // uploader.uploadLevels(event);
            if (event.reply) {
                event.reply(JSON.stringify({success: true}));
            }
        } catch (e) {
            Editor.error(`[${configs.SceneMethods.UploadLevels}]: ${e.message}`);
        }
    },
    "scene-add-item-to-level": (event, args) => {
        try {
            if (!isLevelEditorScene()) {
                Editor.error("Please open a level first before adding items");
                return;
            }
            const n = cc.find(configs.Const.ObjectManagerPath);
            if (!n) {
                Editor.error("Level editor manager not found");
                return;
            }
            addItemToLevel(n, args);
        } catch (e) {
            Editor.error(`[${configs.SceneMethods.AddItemToLevel}]: ${e.message}`);
        }
    },
};

function openLevelEditorScene(args) {
    Editor.assetdb.queryUuidByUrl(
        configs.Const.LevelEditorScenePath,
        (e, uuid) => {
            if (!uuid) {
                Editor.error(
                    `[${configs.SceneMethods.OpenLevel}]: Cannot find scene: ${configs.Const.LevelEditorScenePath}`
                );
                return;
            }
            _Scene.loadSceneByUuid(uuid, (e, t) => {
                if (e) {
                    Editor.error(`[${configs.SceneMethods.OpenLevel}]: ${e.message}`);
                    return;
                }
                openLevel(cc.find(configs.Const.ObjectManagerPath), args);
            });
        }
    );
}

function openLevel(managerNode, args) {
    if (!managerNode) {
        return;
    }
    const c = managerNode.getComponent(configs.Const.ObjectManagerComponent);
    const data = JSON.parse(args);
    c.openLevelByData(data);
}

function exportJson(managerNode, args) {
    if (!managerNode) {
        return;
    }
    const c = managerNode.getComponent(configs.Const.ObjectManagerComponent);
    c.exportJson();
}

function isLevelEditorScene() {
    // Check if we're in the level editor scene by multiple criteria
    try {
        // First check scene name
        const currentScene = cc.director.getScene();
        if (
            currentScene &&
            currentScene.name === configs.Const.LevelEditorSceneName
        ) {
            return true;
        }

        // Fallback: check if the level editor manager exists
        const managerNode = cc.find(configs.Const.ObjectManagerPath);
        if (
            managerNode &&
            managerNode.getComponent(configs.Const.ObjectManagerComponent)
        ) {
            return true;
        }

        return false;
    } catch (e) {
        // If any error occurs, assume we're not in the level editor scene
        Editor.log(`Scene detection error: ${e.message}`);
        return false;
    }
}

function addItemToLevel(managerNode, args) {
    if (!managerNode) {
        return;
    }
    const c = managerNode.getComponent(configs.Const.ObjectManagerComponent);
    c.addItem(args);
}
