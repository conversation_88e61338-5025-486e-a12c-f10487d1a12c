{"name": "ccc-png-auto-compress", "version": "1.2.0.********", "description": "项目构建完成之后自动压缩 PNG 资源", "author": "<PERSON><PERSON><PERSON>（陈皮皮）", "author-info": {"wechat": "im_chenpipi", "email": "<EMAIL>", "git-home": "https://gitee.com/ifaswind", "official-account": "公众号「菜鸟小栈」"}, "repository": {"type": "git", "url": "https://gitee.com/ifaswind/ccc-png-auto-compress"}, "main": "main.js", "main-menu": {"i18n:MAIN_MENU.package.title/i18n:ccc-png-auto-compress.name/i18n:ccc-png-auto-compress.setting": {"message": "ccc-png-auto-compress:open-setting-panel", "icon": "/images/setting.png"}}, "panel.setting": {"main": "panel.setting/index.js", "type": "fixed-size", "title": "i18n:ccc-png-auto-compress.name", "width": 500, "height": 625}, "reload": {"renderer": ["panel.setting/**/*"], "ignore": ["CHANGELOG.md", "README.md", "README.en.md"]}, "_storeId": "a58eb676ce095a2722e6592149b3443e"}