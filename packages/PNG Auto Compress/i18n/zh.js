module.exports = {
    'name': '自动压缩 PNG 资源',
    'setting': '设置',
    // main
    'configSaved': '配置已保存',
    'willCompress': '将在构建完成后自动压缩 PNG 资源...',
    'prepareCompress': '准备压缩 PNG 资源...',
    'notSupport': '不支持当前系统平台',
    'startCompress': '开始压缩 PNG 资源，请勿进行其他操作！',
    'compressDir': '资源目录',
    // setting panel
    'repository': '· 本扩展的 Git 仓库：[ccc-png-auto-compress](https://gitee.com/ifaswind/ccc-png-auto-compress)',
    'apply': '应用',
};
