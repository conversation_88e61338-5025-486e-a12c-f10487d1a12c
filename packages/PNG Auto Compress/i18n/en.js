module.exports = {
    'name': 'PNG Auto Compress',
    'setting': 'Setting',
    // main
    'configSaved': 'Config saved',
    'willCompress': 'PNG resources will be compressed automatically after the build is completed...',
    'prepareCompress': 'Prepare to compress...',
    'notSupport': 'Does not support the current system platform!',
    'startCompress': 'Compress...',
    'compressDir': 'Asset path',
    // setting panel
    'repository': '· Git repository of this extension: [ccc-png-auto-compress](https://gitee.com/ifaswind/ccc-png-auto-compress)',
    'apply': 'Apply',
};
