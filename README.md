# Installation

1. c<PERSON><PERSON> nvm & node 20.
2. t<PERSON>i cocos creator 2.4
3. <PERSON><PERSON><PERSON> đặt WebStorm (dùng key của Rider) mở thư mục project
4. Chạy file `install.sh` để setup project lần đầu
```bash
bash ./install.sh # nếu lỗi thì chạy: sudo bash ./install.sh
```
5. Khi build Android project (Cocos Creator), ko thay đổi Build Path (thư mục `./build`)
6. Android Keystore Path: `./senspark_dev.ks`
7. Dùng Android Studio mở thư mục: `build/jsb-default/frameworks/runtime-src/proj.android-studio` đ<PERSON> build Android & install vào device.

# Entry points:
- Scene: `./assets/scenes/splash_scene.fire`
- Script: `./assets/scripts/scenes/SplashScene.ts`

# Giải thích:
### Code game:
- Nằm ở thư mục `./assets/scripts`
- Read/edit dùng WebStorm
- Nếu sửa nội dung thì CocosCreator sẽ tự build lại

### Code thư viện (Typescript):
- Nằm ở thư mục `./build/jsb-default/frameworks/runtime-src/libraries/ee-x/src/ts`
- Read/edit dùng WebStorm
- Nếu sửa nội dung trong này thì phải manual build lại thư mục này thì chạy lệnh để build lại thư viện:
```bash
bash ./build-lib.sh
# Sau đó chọn Menu trong CocosCreator: Developer > Compile User Scripts
```

### Code thư viện (Android):
- Nằm ở thư mục `./build/jsb-default/frameworks/runtime-src/libraries/ee-x/src/android`
- Read/edit dùng AndroidStudio

# Cloud Save:
1. Chạy server gm7-server local
2. Logic chính nằm trong file `./assets/scripts/manager/game_server/GameServerManager.ts`
3. Cầu nối:
   - Nếu Editor: sẽ sử dụng file `./build/jsb-default/frameworks/runtime-src/libraries/ee-x/src/ts/src/game_server/internal/EditorGameServerMessageBridge.ts`
   - Nếu Android: sẽ sử dụng file `./build/jsb-default/frameworks/runtime-src/libraries/ee-x/src/android/game_server/src/main/java/com/ee/GameSeverBridge.kt`
