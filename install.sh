set -e

FRAMEWORKS_DIR=./build/jsb-default/frameworks

cd $FRAMEWORKS_DIR/runtime-src/libraries/ee-x/src/ts || exit
npm install
npm run build
npm link

cd - || exit
npm install
npm link @senspark/ee-x


curl -o $FRAMEWORKS_DIR/cocos2d-x.zip "http://*************/web/gm7/cocos2d-x.zip"
unzip $FRAMEWORKS_DIR/cocos2d-x.zip -d $FRAMEWORKS_DIR

rm $FRAMEWORKS_DIR/cocos2d-x.zip
rm -rf $FRAMEWORKS_DIR/__MACOSX