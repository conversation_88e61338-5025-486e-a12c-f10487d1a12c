const walk = require('walk');
const fs = require('fs');

const buildDir = './build/web-desktop'
const libPath = `${buildDir}/cocos2d-js-min.js`;
const settingPath = `${buildDir}/src/settings.js`;

let files = [];
let walker = walk.walk(buildDir, {
    followLinks: false
});
walker.on('file', (root, stat, next) => {
    files.push(root + '/' + stat.name);
    next();
});

walker.on('end', () => {
    files.forEach(file => {
        const regex = /\.(json|atlas)$/g;
        if (file.match(regex)) {
            let newFile = file.replace(regex, '.css');
            fs.rename(file, newFile, err => {
                if (err) {
                    console.log(`err = ${err}`);
                } else {
                    console.log(`rename ${file} to ${newFile}`);
                }
            })
        }
    });
});

fs.readFile(libPath, 'utf8', (err, data) => {
    const regex = /"\.json"/g;
    let newData = data.replace(regex, `".css"`);
    if (data !== newData) {
        fs.writeFile(libPath, newData, err => {
            if (err) {
                console.log(`err = ${err}`);
            } else {
                console.log(`modify ${libPath} successfully`);
            }
        });
    }
});

fs.readFile(settingPath, 'utf8', (err, data) => {
    const regex = /\.atlas"/g;
    let newData = data.replace(regex, `.css"`);
    if (data !== newData) {
        fs.writeFile(settingPath, newData, err => {
            if (err) {
                console.log(`err = ${err}`);
            } else {
                console.log(`modify ${settingPath} successfully`);
            }
        }); 
    }
});