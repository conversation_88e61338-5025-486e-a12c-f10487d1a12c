{"name": "mode_1_area_1_lv_9", "children": [{"name": "background", "anchorPoint": [0.5, 0.5], "position": [0, 0], "scale": [1, 1], "rotation": 0, "filePath": "prefabs/event_level_backgrounds/area_1_day.prefab"}, {"name": "railway_1", "anchorPoint": [0.5, 0.5], "position": [0, 300], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/railway_1.prefab"}, {"name": "TNT", "anchorPoint": [0.5, 0.5], "position": [-284, -55], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/TNT.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [-289, -101], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [-290, -148], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [-287, -225], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "TNT", "anchorPoint": [0.5, 0.5], "position": [16, -149], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/TNT.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [11, -101], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [10, -148], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [13, -225], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "TNT", "anchorPoint": [0.5, 0.5], "position": [307, -274], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/TNT.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [302, -101], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [301, -148], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [304, -225], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "TNT", "anchorPoint": [0.5, 0.5], "position": [603, -112], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/TNT.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [598, -101], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [597, -148], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [600, -225], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "TNT", "anchorPoint": [0.5, 0.5], "position": [-593, -188], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/TNT.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [-598, -101], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [-599, -148], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [-596, -225], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "trivia_mouse", "anchorPoint": [0.5, 0.1], "position": [317, -399], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/trivia_mouse.prefab"}, {"name": "trivia_mouse", "anchorPoint": [0.5, 0.1], "position": [-592, -379], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/trivia_mouse.prefab"}, {"name": "trivia_mouse", "anchorPoint": [0.5, 0.1], "position": [29, -379], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/trivia_mouse.prefab"}, {"name": "trivia_mouse", "anchorPoint": [0.5, 0.1], "position": [611, -405], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/trivia_mouse.prefab"}, {"name": "trivia_mouse", "anchorPoint": [0.5, 0.1], "position": [-266, -399], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/trivia_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [919, -595], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [1869, -595], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [1707, -595], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [1527, -595], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [1340, -595], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [1145, -595], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [-2013, -595], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [-1063, -595], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [-1225, -595], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [-1405, -595], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [-1592, -595], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "cart_mouse", "anchorPoint": [0.32, 0], "position": [-1787, -595], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/cart_mouse.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [-886, 192], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [-758, 144], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [-848, 61], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [911, 180], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [756, 195], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [907, 70], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}]}