{"name": "mode_1_area_3_lv_10", "children": [{"name": "background", "anchorPoint": [0.5, 0.5], "position": [0, 0], "scale": [1, 1], "rotation": 0, "filePath": "prefabs/event_level_backgrounds/area_3_day.prefab"}, {"name": "railways_1", "anchorPoint": [0.5, 0.5], "position": [0, 300], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/railway_1.prefab"}, {"name": "claw_chest_0", "anchorPoint": [0.5, 0.5], "position": [763, -547], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/claw_chest_0.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-483, -530], "scale": [1, 1], "rotation": 9, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-503, -361], "scale": [1, 1], "rotation": -25, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-454, -169], "scale": [1, 1], "rotation": -55, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-329, -56], "scale": [1, 1], "rotation": -65, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-163, 5], "scale": [1, 1], "rotation": -65, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-4, 14], "scale": [1, 1], "rotation": -134, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [167, 3], "scale": [-1, 1], "rotation": 43, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [341, -96], "scale": [-1, 1], "rotation": 21, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [481, -236], "scale": [-1, 1], "rotation": 52, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [521, -419], "scale": [-1, 1], "rotation": -15, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [536, -567], "scale": [-1, 1], "rotation": -15, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [10, -379], "scale": [1, 1], "rotation": 90, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-83, -414], "scale": [1, 1], "rotation": 133, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [109, -396], "scale": [1, 1], "rotation": 35, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [135, -489], "scale": [1, 1], "rotation": 3, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-131, -498], "scale": [1, 1], "rotation": 169, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [-202, -415], "scale": [1, 1], "rotation": 118, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [-92, -273], "scale": [1, 1], "rotation": 66, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [96, -259], "scale": [1, 1], "rotation": -158, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [228.8000030517578, -393.1000061035156], "scale": [1, 1], "rotation": 145, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [341, -508], "scale": [1, 1], "rotation": -19, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [322, -428], "scale": [1, 1], "rotation": 34, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [284, -266], "scale": [1, 1], "rotation": 15, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [287, -340], "scale": [1, 1], "rotation": -39, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [218, -211], "scale": [1, 1], "rotation": 77, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [145, -215], "scale": [1, 1], "rotation": 121, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-38, -179], "scale": [1, 1], "rotation": 104, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [41, -187], "scale": [1, 1], "rotation": 53, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-98, -213], "scale": [1, 1], "rotation": 147, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-172.89999389648438, -241.5], "scale": [1, 1], "rotation": 94, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-241.3000030517578, -268.6000061035156], "scale": [1, 1], "rotation": 140, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-256, -330], "scale": [1, 1], "rotation": -166, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-288, -429], "scale": [1, 1], "rotation": 133, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-294, -506], "scale": [1, 1], "rotation": -176, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [-363.6000061035156, -446.5], "scale": [1, 1], "rotation": 127, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [-339.1000061035156, -300.5], "scale": [1, 1], "rotation": 120, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [-262, -180.6999969482422], "scale": [1, 1], "rotation": 79, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [-128.6999969482422, -118.0999984741211], "scale": [1, 1], "rotation": 69, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [14, -106], "scale": [1, 1], "rotation": 43, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [152, -124], "scale": [1, 1], "rotation": 38, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [290, -184], "scale": [1, 1], "rotation": 10, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [366.79998779296875, -323.70001220703125], "scale": [1, 1], "rotation": -34, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [406, -463], "scale": [1, 1], "rotation": -32, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "gold_3", "anchorPoint": [0.5, 0.47], "position": [10, -495], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_3.prefab"}, {"name": "gold_2", "anchorPoint": [0.5, 0.48], "position": [-5, -277], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_2.prefab"}, {"name": "gold_2", "anchorPoint": [0.5, 0.48], "position": [-214, -503], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_2.prefab"}, {"name": "gold_2", "anchorPoint": [0.5, 0.48], "position": [245, -495], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_2.prefab"}, {"name": "gold_2", "anchorPoint": [0.5, 0.48], "position": [192, -306], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_2.prefab"}, {"name": "gold_2", "anchorPoint": [0.5, 0.48], "position": [-177, -322], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_2.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [422, -539], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [377, -398], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [343, -251], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [216, -155], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [86, -134], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [-59, -114], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [-196, -162], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [-315.29998779296875, -229.3000030517578], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [-361, -368], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [-364, -518], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "claw_chest_0", "anchorPoint": [0.5, 0.5], "position": [-735, -528], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/claw_chest_0.prefab"}, {"name": "stone_2", "anchorPoint": [0.5, 0.5], "position": [-840, 110], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/stone_2.prefab"}, {"name": "stone_2", "anchorPoint": [0.5, 0.5], "position": [844, 99], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/stone_2.prefab"}, {"name": "stone_2", "anchorPoint": [0.5, 0.5], "position": [-430, 116], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/stone_2.prefab"}, {"name": "stone_2", "anchorPoint": [0.5, 0.5], "position": [421, 99], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/stone_2.prefab"}]}