[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 14}], "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 1920, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "e9c63cOK0hA5KlykiCcz8ot", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "entityId": "border", "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -100, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 1500}, {"__type__": "cc.Vec2", "x": -100, "y": 1500}], "_id": ""}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "movable": false, "collider": {"__id__": 5}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fecWs2VJtElJqGkgwoWifG", "sync": false}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 20, "_left": 960, "_right": 960, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "e9c63cOK0hA5KlykiCcz8ot", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "entityId": "border", "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -2000, "y": -100}, {"__type__": "cc.Vec2", "x": 2000, "y": -100}, {"__type__": "cc.Vec2", "x": 2000, "y": 0}, {"__type__": "cc.Vec2", "x": -2000, "y": 0}], "_id": ""}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "movable": false, "collider": {"__id__": 11}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05yxr5clJPEbaiGOPZeZll", "sync": false}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1920, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 36, "_left": 960, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "e9c63cOK0hA5KlykiCcz8ot", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "entityId": "border", "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 100, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 1500}, {"__type__": "cc.Vec2", "x": 100, "y": 1500}], "_id": ""}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "movable": false, "collider": {"__id__": 17}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79f7BMyR5FTY1bXuEviNH8", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 140, "_originalHeight": 150, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]