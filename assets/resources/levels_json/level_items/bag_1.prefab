[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "bag_1", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 13}], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "outLine", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 99}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0.871, 0, 0, 0, 0, 1, 1.05, 1.05, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1f859cf8-f682-41aa-ab4c-c6e1b341da7d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "caNawnj9NPVZCgEAji1NWF", "sync": false}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 6}, {"__id__": 8}], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}], "_prefab": {"__id__": 12}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 99}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pullOffset", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2, 42, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2p2VzS11EdJjv49Aze7vM", "sync": false}, {"__type__": "cc.Node", "_name": "cartOffset", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 9}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55lUDt51JODLPVLt++u3Cr", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4deb9bab-55da-4097-b124-04d951e7960f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -0.2, "y": 51.1}, {"__type__": "cc.Vec2", "x": -52.7, "y": 30.8}, {"__type__": "cc.Vec2", "x": -54.2, "y": -44.8}, {"__type__": "cc.Vec2", "x": 52.1, "y": -44.9}, {"__type__": "cc.Vec2", "x": 54, "y": 27.5}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cbL0tC15NB6LchbuW70z6", "sync": false}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 14}], "_prefab": {"__id__": 15}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 99}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0.871, 0, 0, 0, 0, 1, 1.05, 1.05, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1f859cf8-f682-41aa-ab4c-c6e1b341da7d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "65S4UxYdtPcJgy1ZncWXIw", "sync": false}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "movable": false, "collider": {"__id__": 11}, "_id": ""}, {"__type__": "8efc3KyWhZCdJXiv7qfpLL+", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "weight": 0, "offsetTarget": {"__id__": 6}, "_id": ""}, {"__type__": "d5499L0G2BMv7sqVaJ2x8kt", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "entityId": "bag_1", "_view": {"__id__": 5}, "view": {"__id__": 5}, "type": 0, "style": 0, "_dynamiteSpriteFrame": {"__uuid__": "cdf305d4-8531-47ec-8911-c9d39e136bff"}, "_id": ""}, {"__type__": "67b031H6n1KKqvOF5qUNye0", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "offsetTarget": {"__id__": 8}, "_id": ""}, {"__type__": "8d85fZU/TFNSIK+2pvnaw37", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "mask": {"__id__": 13}, "_id": ""}, {"__type__": "7e254bg4oNB2aZ9lo1lwbk5", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_mask": {"__id__": 13}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]