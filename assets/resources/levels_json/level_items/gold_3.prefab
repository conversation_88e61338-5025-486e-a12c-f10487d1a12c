[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "gold_3", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 190, "height": 175}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.47}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pullOffset", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 89, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "806cPAZx9BgrNInqjBZHAt", "sync": false}, {"__type__": "cc.Node", "_name": "cartOffset", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 5}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -77, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07zPaSS8ZPrrcE3GzNLtnc", "sync": false}, {"__type__": "cc.Node", "_name": "outLine", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 171}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [7, 9, 0, 0, 0, 0, 1, 1.05, 1.05, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b33cfc61-66e0-4afa-ac74-aab009c3e9b9"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8dY73iyFG+JCI1FpESZKB", "sync": false}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 10}], "_active": true, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": {"__id__": 15}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 198}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}], "_prefab": {"__id__": 12}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 171}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [7, 9, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b33cfc61-66e0-4afa-ac74-aab009c3e9b9"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8do5VW25xDVbxvGDlIEHvb", "sync": false}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "", "defaultAnimation": "idle-gold", "_preCacheMode": 1, "_cacheMode": 1, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "idle-gold", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "79e5c0c1-a678-4854-b2a5-d23fb1fca2f5"}, "_N$_defaultCacheMode": 1, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": true, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 60.4, "y": -67.9}, {"__type__": "cc.Vec2", "x": 85.7, "y": -41.9}, {"__type__": "cc.Vec2", "x": 94, "y": -14}, {"__type__": "cc.Vec2", "x": 87.2, "y": 34.8}, {"__type__": "cc.Vec2", "x": 68.6, "y": 72.1}, {"__type__": "cc.Vec2", "x": 53.1, "y": 83}, {"__type__": "cc.Vec2", "x": -25, "y": 91.3}, {"__type__": "cc.Vec2", "x": -72.8, "y": 55.5}, {"__type__": "cc.Vec2", "x": -91.7, "y": -6.5}, {"__type__": "cc.Vec2", "x": -78.2, "y": -41.7}, {"__type__": "cc.Vec2", "x": -43.1, "y": -66.9}, {"__type__": "cc.Vec2", "x": 14.4, "y": -75.2}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50Yw3HLutJSrNOUJyIQkLv", "sync": false}, {"__type__": "98ed6OWEKhKl6dBqxuWIELo", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "entityId": "gold_3", "_view": {"__id__": 9}, "type": 3, "_id": ""}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "movable": false, "collider": {"__id__": 14}, "_id": ""}, {"__type__": "8efc3KyWhZCdJXiv7qfpLL+", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "weight": 9, "offsetTarget": {"__id__": 2}, "_id": ""}, {"__type__": "67b031H6n1KKqvOF5qUNye0", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "offsetTarget": {"__id__": 4}, "_id": ""}, {"__type__": "63bd3piY/VFkpt05l6mn+js", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "value": 500, "_id": ""}, {"__type__": "8d85fZU/TFNSIK+2pvnaw37", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "mask": {"__id__": 10}, "_id": ""}, {"__type__": "7e254bg4oNB2aZ9lo1lwbk5", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_mask": {"__id__": 10}, "_pickSize": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]