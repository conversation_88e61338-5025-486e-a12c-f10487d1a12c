import assert = require('assert');
import {DialogUtils} from '../../../dialog/DialogUtils';
import {NoAdsDialog} from '../../../dialog/NoAdsDialog';
import * as gm from '../../../engine/gm_engine';
import {ChestType} from '../../../engine/gm_engine';
import {MoreCurrencyUtils} from '../../../game/more_currency_dialog/MoreCurrencyUtils';
import * as ee from '../../../libraries/ee/index';
import {
    AudioManager,
    BoosterStorageManager,
    CrashlyticManager,
    crashlytics,
    GameMode, LevelManager,
    RawReward,
    RewardInfo,
    RewardManager,
    SceneName,
    SoundType,
    StoreItem,
    StoreManager,
    TrackAdsRewardWatch,
    TrackingManager,
    TrackResultIap,
    TrackResultSoftCurrency,
    TrackSinkType,
    WatchAdsHelper,
} from '../../../manager/gm_manager';
import {LevelScore} from '../../common/LevelScore';
import {LevelController, NullLevelController,} from '../LevelController';
import {StoryLevelConfig} from '../StoryLevelConfig';
import {StoryLevelProgress} from '../StoryLevelProgress';
import {CloseDialogButton} from "../../../ui/CloseDialogButton";
import {RewardView} from "../../common/RewardView";
import {HiddenTempleManager} from "../../hidden_temple/HiddenTempleManager";
import {PiggyBankManager} from "../../PiggyBank/PiggyBankManager";
import {PlayPassManager} from "../../play_pass/PlayPassManager";
import {WinningStreakManager} from "../../winning_streak/WinnngStreakManager";
import {ServiceLocator} from "../../../libraries/ee/index";

const {ccclass, disallowMultiple, property} = cc._decorator;

/** Appears when the level time runs out. */
export abstract class LevelTimeOutDialog extends ee.Dialog {
    /** Sets the level controller. */
    public abstract setController(controller: LevelController): void;

    public abstract setConfig(config: StoryLevelConfig): void;

    public abstract setRewardInfo(xp: number, gold: number, gameMode: GameMode, cardRewards?: RawReward[], pulledChest?: gm.ChestType): Promise<void>;

    public abstract setProgress(progress: StoryLevelProgress): void;

    public abstract setTimeOuts(timeOuts: number): this;

    //public abstract setIsEvent(bool: boolean): void;

    public abstract setPulledChest(isChestCollected: boolean, pulledChest?: gm.ChestType): void;
}

/** Used in editor. */
@ccclass
@disallowMultiple
class DefaultLevelTimeOutDialog extends LevelTimeOutDialog {
    @ee.nest(LevelScore)
    private readonly _levelScore: LevelScore | null = null;

    private get levelScore(): LevelScore {
        return gm.retrieveNull(this._levelScore);
    }

    @property({type: cc.Label, visible: true})
    private readonly _priceLabel: cc.Label | null = null;

    private get priceLabel(): cc.Label {
        return gm.retrieveNull(this._priceLabel);
    }

    @property({type: cc.Sprite, visible: true})
    private readonly _currencyIcon: cc.Sprite | null = null;

    private get currencyIcon(): cc.Sprite {
        return gm.retrieveNull(this._currencyIcon);
    }

    @property({type: cc.Node, visible: true})
    private readonly _adsNode: cc.Node | null = null;

    private get adsNode(): cc.Node {
        return gm.retrieveNull(this._adsNode);
    }

    @property({type: [cc.SpriteFrame], visible: true})
    private readonly _iconSpriteFrames: cc.SpriteFrame[] = [];

    @property({type: cc.Node, visible: true})
    private readonly _chestLayer: cc.Node | null = null;

    private get chestLayer(): cc.Node {
        return gm.retrieveNull(this._chestLayer);
    }

    @property({type: cc.Sprite, visible: true})
    private readonly _chestIcon: cc.Sprite | null = null;

    @property({type: [cc.SpriteFrame], visible: true})
    private readonly _chestSpriteFrames: cc.SpriteFrame[] = [];

    @property({type: CloseDialogButton, visible: true})
    private _btnCloseDialog: CloseDialogButton = null;

    // @property(cc.ScrollView)
    // private rewardScrollView: cc.ScrollView = null;
    //
    // @property(cc.Prefab)
    // private rewardViewPrefab: cc.Prefab = null;
    //
    // @property(cc.Layout)
    // private rewardLayout: cc.Layout = null;

    @property(CloseDialogButton)
    private closeInnerFrameButton: CloseDialogButton = null;

    @property(cc.Node)
    private extraTimeFrame: cc.Node = null;

    @property(cc.Node)
    private rewardFrame: cc.Node = null;

    @property(cc.Sprite)
    private winningIcon: cc.Sprite | null = null;

    @property([cc.SpriteFrame])
    private winningIcons: cc.SpriteFrame[] = [];

    @property(cc.Node)
    private winningStreakFrame: cc.Node = null;

    @property(cc.Node)
    private iconGold: cc.Node = null;

    @property(cc.Node)
    private iconRubyGold: cc.Node = null;

    private get iconSpriteFrames(): cc.SpriteFrame[] {
        return gm.retrieveNull(this._iconSpriteFrames);
    }

    private clickable = true;

    /** Level progress and level target score. */
    private progress = 0;
    private target = 0;

    /** The current level controller. */
    private controller: LevelController = new NullLevelController();

    /** Cost to revive. */
    private cost = 0;

    /** Currency cost type (ruby or gold). */
    private costType = StoreItem.Ruby;

    /** Currently using boosters in the current level. */
    private activeBoosters: gm.BoosterType[] = [];
    private currentArea = 0;
    private currentLevel = 0;
    private gameMode: GameMode;

    private isEvent = false;

    protected constructor() {
        super();
        DialogUtils.addFadeInBackground(this);
        DialogUtils.addPopOutTransition(this);
        DialogUtils.addPopInTransition(this);
        DialogUtils.addFadeOutBackground(this);
    }

    @crashlytics
    protected onLoad(): void {
        super.onLoad();
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        ee.ServiceLocator.resolve(TrackingManager).trackEventOpen(SceneName.DialogStoryOutOfTime);

        //assert(this._boosterPanel !== null);
        assert(this._levelScore !== null);
        //assert(this._timeLabel !== null);
        assert(this._priceLabel !== null);
        assert(this._currencyIcon !== null);
        assert(this._adsNode !== null);

        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.failLevel();
        });
        //this.boosterPanel.setSceneName(SceneName.DialogStoryOutOfTime);
        this._btnCloseDialog.replaceCallback(this.failLevel.bind(this));
        this.closeInnerFrameButton.replaceCallback(() => {
            this.closeInnerFrameButton.node.active = false;
            this.extraTimeFrame.active = false;

            if (this.gameMode != GameMode.STORY) {
                this.rewardFrame.active = true;
                return;
            }

            const levelStreak = ee.ServiceLocator.resolve(WinningStreakManager).levelStreak;
            if (levelStreak > 0) {
                this.winningIcon.spriteFrame = this.winningIcons[levelStreak];
                this.winningStreakFrame.active = true;
            } else {
                this.rewardFrame.active = true;
            }
        })
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    /** Sets the level controller. */
    public setController(controller: LevelController): void {
        this.controller = controller;
    }

    /** Sets the active boosters. */
    // public setActiveBoosters(boosters: gm.BoosterType[]): void {
    //     this.activeBoosters = boosters;
    //     this.updateBoosterPanel();
    // }

    /** Sets the level config. */
    public setConfig(config: StoryLevelConfig): void {
        this.currentArea = config.area;
        this.currentLevel = config.level;
        this.target = config.targets[0];
        this.updateProgress();
    }

    public async setRewardInfo(xp: number, gold: number, gameMode: GameMode, cardRewards?: RawReward[], pulledChest?: ChestType) {
        this.gameMode = gameMode;

        // Tính ruby reward nếu chiến thắng...
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        levelManager.setRubyReward(this.gameMode, this.currentArea, this.currentLevel);
        this.iconRubyGold.active = levelManager.rubyReward > 0;
        this.iconGold.active = levelManager.rubyReward <= 0;

        // let totalRewards = [];
        // let rewardManager = ee.ServiceLocator.resolve(RewardManager);
        //
        // if (gameMode === GameMode.STORY) {
        //     const area = this.currentArea + 1;
        //     const level = this.currentLevel + 1;
        //     const hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);
        //     if (hiddenTempleManager.isUnlock(area, level)) {
        //         const axeReward = hiddenTempleManager.getAxeReward(area, level);
        //         if (axeReward > 0) {
        //             totalRewards.push(rewardManager.createReward({
        //                 type: 'store',
        //                 subType: 'axe',
        //                 value: axeReward
        //             }));
        //         }
        //     }
        // }
        //
        // const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
        // playPassManager.winLevel = true;
        // const passReward = playPassManager.completeLevel();
        // if (passReward > 0) {
        //     totalRewards.push(rewardManager.createReward({
        //         type: 'store',
        //         subType: playPassManager.isGold ? 'gold_pass' : 'free_pass',
        //         value: passReward
        //     }));
        // }
        //
        // totalRewards.push(rewardManager.createReward({
        //     type: 'store',
        //     subType: 'xp',
        //     value: xp
        // }));
        //
        // totalRewards.push(rewardManager.createReward({
        //     type: 'store',
        //     subType: 'gold',
        //     value: gold,
        // }));
        //
        // // Cộng dồn số lượng của các random cards
        // let cardQuantity = 0;
        // if (cardRewards?.length) {
        //     for (let r of cardRewards) {
        //         cardQuantity += r.value;
        //
        //         // Bỏ hiển thị các random cards...
        //         //totalRewards.push(rewardManager.createReward(r));
        //     }
        // }
        // // ... mà hiển thị 1 card_back đại diện cho các cards random sau này khi chiến thắng
        // if(cardQuantity > 0){
        //     totalRewards.push(rewardManager.createReward({
        //         type: 'card',
        //         subType: 'card_back',
        //         value: cardQuantity,
        //     }))
        // }
        //
        //
        // if (pulledChest !== undefined) {
        //     const subTypeDict = {
        //         [ChestType.Copper]: 'copper',
        //         [ChestType.Diamond]: 'diamond',
        //         [ChestType.Gold]: 'gold',
        //         [ChestType.Silver]: 'silver',
        //         [ChestType.Star]: 'star'
        //     }
        //     totalRewards.push(rewardManager.createReward({
        //         type: 'chest',
        //         subType: subTypeDict[pulledChest],
        //         value: 1
        //     }))
        // }
        //
        // this.setPulledChest(pulledChest !== undefined, pulledChest);
        //
        // const resolvedItems = await Promise.all(
        //     totalRewards.map(async (reward: RewardInfo) => {
        //         const item = cc.instantiate(this.rewardViewPrefab);
        //         await item.getComponent(RewardView).setRewardInfo(reward);
        //         return item;
        //     })
        // );
        //
        // // Add all resolved items to the layout
        // resolvedItems.forEach((item) => {
        //     item.getChildByName("layout").active = false;
        //     this.rewardLayout.node.addChild(item);
        // });
        // this.scheduleOnce(()=>{
        //     this.rewardScrollView.scrollToLeft();
        // })

    }

    public setProgress(progress: StoryLevelProgress): void {
        this.progress = progress.progress;
        this.updateProgress();
    }

    public setTimeOuts(timeOuts: number): this {
        // Bỏ hồi sinh bằng Ads & Gold
        // {
        //     this.adsNode.active = timeOuts < 2;
        //     const isFirstTimeOut = timeOuts < 1;
        //     this.costType = isFirstTimeOut ? StoreItem.Gold : StoreItem.Ruby;
        // }
        // Chỉ cho hồi sinh bằng Ruby
        this.adsNode.active = true;
        const isFirstTimeOut = false;
        this.costType = StoreItem.Ruby;

        const boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        const boosterCost = boosterStorageManager.getCost(gm.BoosterType.Clock, this.costType);

        this.cost = boosterCost;

        // Update price label and cost type icon.
        this.priceLabel.string = `${this.cost}`;
        this.currencyIcon.spriteFrame = this.iconSpriteFrames[isFirstTimeOut ? 0 : 1];
        return this;
    }

    public setPulledChest(isChestCollected: boolean, pulledChest?: gm.ChestType): void {
        let iconChest = {
            [gm.ChestType.Copper]: this._chestSpriteFrames[0],
            [gm.ChestType.Diamond]: this._chestSpriteFrames[1],
            [gm.ChestType.Free]: this._chestSpriteFrames[2],
            [gm.ChestType.Gold]: this._chestSpriteFrames[3],
            [gm.ChestType.Silver]: this._chestSpriteFrames[4],
            [gm.ChestType.Star]: this._chestSpriteFrames[5],
        };
        this.chestLayer.active = isChestCollected;
        if (isChestCollected && pulledChest !== undefined) {
            this._chestIcon.spriteFrame = iconChest[pulledChest];
        }
    }

    // public setIsEvent(isEvent: boolean): this {
    //     this.isEvent = isEvent;
    //     this.updateBoosterPanel();
    //     return this;
    // }

    /** Occurs when active boosters or current level or game mode changes. */
    private updateBoosterPanel(): void {
        // const boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        // const levelManager = ee.ServiceLocator.resolve(LevelManager);
        // const area = levelManager.getLastUnlockedArea();
        // const info = levelManager.getStoryAreaInfo(area);
        // const boosters = this.isEvent
        //     ? boosterStorageManager.getEventBoosters()
        //     : boosterStorageManager.getStoryBoosters(
        //         this.currentArea,
        //         this.currentLevel,
        //         area,
        //         info.unlockedLevels - 1);
        //this.boosterPanel.boosters = boosters.filter(item => this.activeBoosters.lastIndexOf(item.type) === -1 || item.type === gm.BoosterType.Dynamite);
    }

    /** Occurs when progress or level targets changes. */
    private updateProgress(): void {
        this.levelScore.setScore(this.target - this.progress);
    }

    /** Forces to fails the current level. */
    private failLevel(): void {
        this.onDidHide(() => {

            if (this.gameMode === GameMode.STORY) {
                // ghi nhận winning streak thua
                ServiceLocator.resolve(WinningStreakManager).levelLose();
            }
            this.controller.failLevel();
            //this.boosterPanel.removeBoosterPopUpInfo();
        });
        this.hide();
    }

    /** Register in editor. */
    @crashlytics
    private onOutsidePressed(): void {
        if (!this.isActive()) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onOutsidePressed, `${this.uuid}`);
        this.failLevel();
    }

    private afterBuyMoreCurrency() {
        const storeManager = ee.ServiceLocator.resolve(StoreManager);
        const balance = storeManager.getItemBalance(this.costType);
        if (this.cost <= balance) {
            this.applyBoosterThenContinue();
        }
    }

    /** Register in editor. */
    @crashlytics
    private onBuyButtonPressed(): void {
        if (!this.isActive()) {
            return;
        }
        if (!this.clickable) {
            return;
        }
        this.clickable = false;
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onBuyButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.DialogStoryOutOfTime, 'btn_continue');
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        const storeManager = ee.ServiceLocator.resolve(StoreManager);
        const balance = storeManager.getItemBalance(this.costType);
        if (this.cost <= balance) {
            this.applyBoosterThenContinue();
        } else {
            this.showMoreCurrencyDialog().then(() => {
                this.scheduleOnce(()=>{
                    this.clickable = true;
                    this.onBuyButtonPressed();
                }, 1.5)
            });
        }
    }

    private applyBoosterThenContinue(): void {
        const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
        piggyBankManager.autoCashBack = false;
        ee.ServiceLocator.resolve(StoreManager).addItemBalance(this.costType, -this.cost);
        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        this.costType === StoreItem.Ruby
            ? trackingManager.trackEventSinkIap(SceneName.DialogStoryOutOfTime,
                TrackResultIap.Bought, TrackSinkType.Ruby, `level_time_out_ruby`, this.cost)
            : trackingManager.trackEventSinkSoftCurrency(SceneName.DialogStoryOutOfTime,
                TrackResultSoftCurrency.Bought, TrackSinkType.GoldBar, this.cost);

        const boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        boosterStorageManager.addBalance(gm.BoosterType.Clock, 1);
        this.onDidHide(() => {
            this.controller.applyMoreBoosters([gm.BoosterType.Clock]);
        });
        this.hide();
    }

    private async showMoreCurrencyDialog(): Promise<void> {
        await MoreCurrencyUtils.show({
            parentDialog: this,
            sceneName: SceneName.DialogStoryOutOfTime,
            storeItem: this.costType,
            needed: this.cost,
            callback: (pressed) => {
                if (pressed) {
                    this.afterBuyMoreCurrency();
                }
            }
        });
    }

    /** Registered in editor. */
    @crashlytics
    private onAdsButtonPressed(): void {
        if (!this.isActive()) {
            return;
        }
        if (!this.clickable) {
            return;
        }
        this.clickable = false;
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onAdsButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        // watch ads to get more 10 seconds
        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        trackingManager.trackEventClick(SceneName.DialogStoryOutOfTime, 'btn_video');

        (new WatchAdsHelper(SceneName.DialogStoryOutOfTime, TrackAdsRewardWatch.ClockBoosterBonusStory)).onSuccess(() => {
            const boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
            //const boosters = this.boosterPanel.getSelectedBoosters();
            boosterStorageManager.addBalance(gm.BoosterType.ClockAds, 1);
            this.onDidHide(() => {
                this.controller.applyMoreBoosters([gm.BoosterType.ClockAds]);
            });
            this.hide();
        }).onNoAds(() => {
            NoAdsDialog.create().then(dialog => {
                dialog.onWillShow(() => {
                });
                this.showDialog(dialog);
                dialog.onDidHide(() => {
                    this.clickable = true;
                });
            });
        }).start();
    }
}