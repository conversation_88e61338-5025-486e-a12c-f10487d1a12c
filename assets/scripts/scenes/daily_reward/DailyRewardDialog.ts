import {DailyRewardCell, DailyRewardCellImpl, DailyRewardState} from "./DailyRewardCell";
import {DailyRewardManager, DailyRewardTemplate} from "../../manager/daily_reward/DailyRewardManager";
import {DailyRewardCategory} from "./DailyRewardCategory";
import {NoAdsDialog} from "../../dialog/NoAdsDialog";
import {CommonDialog} from "../../dialog/CommonDialog";
import * as ee from "../../libraries/ee/index";
import * as gm from '../../engine/gm_engine';
import {
    AudioManager,
    CrashlyticManager,
    crashlytics,
    SceneName,
    SoundType,
    TrackAdsRewardWatch,
    TrackingManager,
    WatchAdsHelper,
} from "../../manager/gm_manager";

const {ccclass, property} = cc._decorator;

@ccclass
export class DailyRewardDialog extends CommonDialog {

    @ee.nest([DailyRewardCellImpl])
    private _listCell: DailyRewardCellImpl[] = [];

    @property({type: cc.ScrollView, visible: true})
    private _scrollView: cc.ScrollView | null = null;
    private get scrollView(): cc.ScrollView {
        return gm.retrieveNull(this._scrollView);
    }

    @property({type: cc.Layout, visible: true})
    private _layout: cc.Layout | null = null;

    @property({type: cc.Button, visible: true})
    private _buttonClaim: cc.Button | null = null;

    @property({type: cc.Button, visible: true})
    private _buttonClaimX2: cc.Button | null = null;

    @property({type: cc.Node, visible: true})
    private _skipText: cc.Node | null = null;

    private get layout(): cc.Layout {
        return gm.retrieveNull(this._layout);
    }

    private get buttonClaim(): cc.Button {
        return gm.retrieveNull(this._buttonClaim);
    }

    private get buttonClaimX2(): cc.Button {
        return gm.retrieveNull(this._buttonClaimX2);
    }

    private _dailyRewardManager?: DailyRewardManager;

    private get dailyRewardManager(): DailyRewardManager {
        if (this._dailyRewardManager === undefined) {
            this._dailyRewardManager = ee.ServiceLocator.resolve(DailyRewardManager);
        }
        return this._dailyRewardManager;
    }

    private _template?: DailyRewardTemplate;
    private clickable: boolean = true;

    private get template(): DailyRewardTemplate {
        if (this._template === undefined) {
            this._template = this.dailyRewardManager.getTemplate();
        }
        return this._template;
    }

    private scrollToIndex(index: number): void {
        const children = this.layout.node.children;
        const arrayIdx = Math.max(0, Math.min(children.length - 1, index));
        const cellNode = children[arrayIdx];
        if (cellNode) {
            this.scrollToCell(cellNode);
        }
    }

    private scrollToCell(cell: cc.Node): void {
        this.scrollView.scrollToOffset(cc.v2(0, -cell.getBoundingBox().yMax));
    }

    @crashlytics
    protected onLoad() {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);

        super.onLoad();
        this.updateRewardInfo();
        // this.setTouchOutsideEnabled(false);
        this.updateControl();

        // Track dialog open event
        ee.ServiceLocator.resolve(TrackingManager).trackEventOpen(SceneName.SceneDailyReward);
    }

    private updateRewardInfo() {
        const currentIndex = this.dailyRewardManager.getCurrentRewardIndex();
        let todayRewardAvailable = this.dailyRewardManager.isRewardAvailable();

        this._listCell.forEach((item, index) => {
            item.dayIndex = index;
            if (index < currentIndex) {
                item.state = DailyRewardState.Received;
            } else if (index === currentIndex) {
                const state = this.dailyRewardManager.isRewardAvailable()
                    ? DailyRewardState.Claim
                    : DailyRewardState.Received;
                item.state = state;
            } else {
                item.state = DailyRewardState.NotClaim;
            }

            // Ensure template and days exist before accessing
            if (this.template && this.template.days && this.template.days[index]) {
                item.rewards = this.template.days[index].items;
            }
        });

        // If there are no rewards, hide two buttons claim and show skip text
        if (!todayRewardAvailable) {
            this.buttonClaim.node.active = false;
            this.buttonClaimX2.node.active = false;
            this.setTouchOutsideEnabled(true);
            this._skipText.active = true;
        } else {
            this.buttonClaim.node.active = true;
            this.buttonClaimX2.node.active = true;
            this.setTouchOutsideEnabled(false);
            this._skipText.active = false;
        }
    }

    @crashlytics
    private updateControl() {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.updateControl, `${this.uuid}`);

        // Set event on click for button claim to claim reward from cell that is state Claim
        if (this.buttonClaim && this.buttonClaim.node) {
            this.buttonClaim.node.on(cc.Node.EventType.TOUCH_END, this.onClaimPressed.bind(this));
        }

        // Watch Ads to claim x2
        if (this.buttonClaimX2 && this.buttonClaimX2.node) {
            this.buttonClaimX2.node.on(cc.Node.EventType.TOUCH_END, this.onWatchAdsPressed.bind(this));
        }
    }

    /**
     * Handle normal claim button press
     */
    @crashlytics
    private onClaimPressed(): void {
        if (!this.clickable) {
            return;
        }

        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onClaimPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneDailyReward, 'btn_claim');

        this._listCell.forEach((item) => {
            if (item.state === DailyRewardState.Claim) {
                item.onClaim();
            }
        });

        this.hide();
    }

    @crashlytics
    private onWatchAdsPressed(): void {
        if (!this.clickable) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onWatchAdsPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneDailyReward, 'btn_watch_ads');

        this.clickable = true;
        this.watchAds().then(() => {
            this.clickable = false;
            try {
                this._listCell.forEach((item) => {
                    if (item.state === DailyRewardState.Claim) {
                        item.onClaimWithAds();
                    }
                });

                this.hide();
            } catch (error) {
                cc.error("Error claiming daily reward with ads:", error);
            }
        }).catch((error) => {
            this.clickable = false;
            cc.error("Error watching ads for daily reward:", error);
        });
    }

    private async watchAds(): Promise<void> {
        return await new Promise((resolve, reject) => {
            try {
                (new WatchAdsHelper(SceneName.SceneDailyReward, TrackAdsRewardWatch.ClaimDailyReward))
                    .onSuccess(() => {
                        cc.log("Successfully watched ads for daily reward");
                        resolve();
                    })
                    .onNoAds(() => {
                        NoAdsDialog.create().then(dialog => this.showDialog(dialog));
                        resolve();
                    })
                    .start();
            } catch (error) {
                cc.error("Error starting ads for daily reward:", error);
                reject(error);
            }
        });
    }
}
