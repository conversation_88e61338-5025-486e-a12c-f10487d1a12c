node_modules/**
!node_modules/**/
!node_modules/**/*.meta

#/////////////////////////////////////////////////////////////////////////////
# Fireball Projects
#/////////////////////////////////////////////////////////////////////////////

library/
temp/
local/
# build/

#/////////////////////////////////////////////////////////////////////////////
# Logs and databases
#/////////////////////////////////////////////////////////////////////////////

*.log
*.sql
*.sqlite

#/////////////////////////////////////////////////////////////////////////////
# files for debugger
#/////////////////////////////////////////////////////////////////////////////

*.sln
*.csproj
*.pidb
*.unityproj
*.suo

#/////////////////////////////////////////////////////////////////////////////
# OS generated files
#/////////////////////////////////////////////////////////////////////////////

.DS_Store
ehthumbs.db
Thumbs.db

#/////////////////////////////////////////////////////////////////////////////
# exvim files
#/////////////////////////////////////////////////////////////////////////////

*UnityVS.meta
*.err
*.err.meta
*.exvim
*.exvim.meta
*.vimentry
*.vimentry.meta
*.vimproject
*.vimproject.meta
.vimfiles.*/
.exvim.*/
quick_gen_project_*_autogen.bat
quick_gen_project_*_autogen.bat.meta
quick_gen_project_*_autogen.sh
quick_gen_project_*_autogen.sh.meta
.exvim.app

#/////////////////////////////////////////////////////////////////////////////
# webstorm files
#/////////////////////////////////////////////////////////////////////////////

.idea/

#//////////////////////////
# VS Code
#//////////////////////////

.vscode/
creator.d.ts
texture_packer/packed
build/jsb-default/frameworks/cocos2d-x
build/jsb-default/frameworks/runtime-src/proj.ios_mac/Pods
build/jsb-default/res
build/jsb-default/src
build/jsb-default/**/*.js
*.xcworkspace
build/jsb-default/frameworks/runtime-src/proj.ios_mac/gmworldtour.xcodeproj/xcuserdata
build/web-mobile
build/jsb-default/frameworks/runtime-src/proj.android-studio/app/.externalNativeBuild
gmworldtour.iml
libcocos2d iOS.a
build/jsb-default/frameworks/runtime-src/proj.win32
Podfile.lock
build/jsb-default/frameworks/runtime-src/proj.ios_mac/build
ads_backup
build/web-desktop
build/fb-instant-games
*.hprof
scripts/node_modules
build_
build1
project.json
cocos2d
build/jsb-default/assets/
assets/scripts/manager/level/DefaultServerConfig.ts
build/jsb-default/frameworks/runtime-src/libraries/**/build/
build/jsb-default/frameworks/runtime-src/libraries/ts/node_modules
build/jsb-default/frameworks/runtime-src/libraries/ts/dist
build/jsb-default/frameworks/runtime-src/proj.android-studio/.kotlin
